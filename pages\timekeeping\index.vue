<template>
  <div class="m-2 h-screen-50 overflow-y-auto">
    <div class="grid grid-cols-1 md:grid-cols-12 gap-2">
      <div class="col-span-12 md:col-span-4">
        <NavigationTimeKeeping
          @createWorkEffort="handleCreateWorkEffort"
          @selectWorkEffort="handleSelectWorkEffort"
          :dataWorkEffort="dataWorkEffort"
          :selectedWorkEffortId="selectedWorkEffortId"
        ></NavigationTimeKeeping>
      </div>
      <div class="col-span-12 md:col-span-8">
        <TabTimeKeeping
          :selectedWorkEffortId="selectedWorkEffortId"
        ></TabTimeKeeping>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
useHead({
  title: "Chấm công",
  meta: [
    {
      name: "description",
      content: "timekeeping",
    },
  ],
});

definePageMeta({
  layout: "dashboard",
  middleware: ["auth", "permission", "store"],
  permission: [
    "SALE_OP",
    "SUPP_ADMIN",
    "ORG_ADMIN",
    "SALE_MANAGER",
    "SUPP_OP",
    "SALE",
    "SALES",
  ],
  name: "Chấm công",
});

const { getWorkEffort, createWorkEffort } = useCrm();
const auth = useCookie("auth") as any;
const dataWorkEffort = ref<any>([]);
const selectedWorkEffortId = ref<any>();

const handleGetWorkEffort = async () => {
  try {
    const response = await getWorkEffort(
      auth.value?.user?.id,
      "CHECKIN",
      "SHIFT",
      "",
      ""
    );
    dataWorkEffort.value = response?.data;
    if (response?.data?.length > 0 && !selectedWorkEffortId.value) {
      selectedWorkEffortId.value = response?.data[0]?.id;
    }
  } catch (error) {
    console.error("Error fetching work effort:", error);
  }
};

const handleCreateWorkEffort = async () => {
  try {
    await createWorkEffort(
      auth.value?.user?.id,
      "Check In",
      "Bắt đầu ca làm việc",
      "CHECKIN",
      "SHIFT"
    );
    await handleGetWorkEffort();
  } catch (error) {
    console.error("Error creating work effort:", error);
  }
};

const handleSelectWorkEffort = (workEffortId: string) => {
  selectedWorkEffortId.value = workEffortId;
};

onMounted(() => {
  handleGetWorkEffort();
});
</script>
