<template>
  <div class="my-2 rounded bg-secondary">
    <textarea
      rows="2"
      id="tag"
      v-model="tagDescription"
      class="py-1 px-2 w-full text-base rounded outline-none bg-secondary border-b"
      placeholder="<PERSON><PERSON> tả tình huống"
      :disabled="isNotDraft"
      @blur="updateTagDescription"
    ></textarea>

    <div class="flex" @click.stop="focusInput">
      <div class="w-full rounded text-sm relative">
        <div class="flex flex-wrap gap-1 m-[2px]">
          <span
            v-for="(tag, index) in dataTag"
            :key="index"
            class="relative text-base border px-3 py-1 rounded-2xl bg-white flex items-center"
          >
            {{ tag?.title }}
            <span
              v-if="!isNotDraft"
              class="absolute -top-1 -right-1 text-red-500 cursor-pointer"
              @click="handleRemoveTag(tag)"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
                class="w-3 h-3"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  d="M9.75 9.75l4.5 4.5m0-4.5l-4.5 4.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"
                />
              </svg>
            </span>
          </span>
          <input
            v-if="!isNotDraft"
            class="focus:outline-none max-w-[100px] w-full px-2 py-1 bg-secondary relative text-base"
            v-model.lazy="tagTitle"
            :key="inputKey"
            placeholder="Nhập tag"
            @keyup.enter="handleAddOrCreateTag"
            @input="handleInputChange"
            ref="inputRef"
            :id="tag?._id"
          />
          <div
            v-if="dataSearchTag?.length > 0 && tagTitle"
            class="absolute w-full max-h-[150px] z-50"
            style="top: 100%"
          >
            <div
              class="border max-h-[100px] w-full overflow-y-auto p-2 bg-white rounded-md space-y-1"
            >
              <div
                v-for="search in dataSearchTag"
                :key="search.id"
                @click.prevent="handleAddTag(search)"
                class="cursor-pointer hover:bg-gray-100 p-1 flex items-center justify-between"
              >
                <span class="mb-1">
                  <span>{{ search.title }}</span>
                </span>
                <div v-if="search.id === 'addTAG'">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                    class="w-4 h-4"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      d="M12 4.5v15m7.5-7.5h-15"
                    />
                  </svg>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup>
import { ref, computed, onMounted, nextTick } from "vue";

const {
  updateConnectorDescription,
  searchTag,
  createTag,
  addTag,
  getTags,
  removeTag,
} = usePortal();
const props = defineProps(["tag"]);
const orderStore = useOrderStore();

const inputRef = ref(null);

const isNotDraft = computed(() => orderStore.isNotDraft);
const tagDescription = ref(props.tag.description);
const tagTitle = ref("");
const dataTag = ref([]);
const dataSearchTag = ref([]); // Đảm bảo dataSearchTag luôn là một mảng

// Fetch tags on component mount
const fetchTags = async () => {
  try {
    dataTag.value = await getTags(props.tag?._id);
  } catch (error) {
    console.error("Error fetching tags:", error);
  }
};

const updateTagDescription = async () => {
  const auth = useCookie("auth").value;
  await updateConnectorDescription(
    props.tag._id,
    tagDescription.value,
    auth?.user?.id
  );
};

const handleAddTag = async (tag) => {
  if (tag.id === "addTAG") {
    await createNewTag();
  } else {
    await saveTag(tag);
  }
};

const focusInput = () => {
  // Thêm một độ trễ nhỏ trước khi focus vào input để tránh bàn phím đóng lại ngay
  nextTick(() => {
    inputRef.value?.focus();
  });
};
//

const inputKey = ref(0); // Khởi tạo một biến để quản lý key của input

const saveTag = async (tag) => {
  try {
    // Thêm tag vào hệ thống
    tagTitle.value = "";
    const auth = useCookie("auth").value;
    await addTag(props.tag?._id, tag.title, tag.id, auth?.user?.id);
    await fetchTags();

    nextTick(() => {
      inputKey.value++; // Tăng key để trigger render lại input
      tagTitle.value = ""; // Clear tagTitle sau khi render lại input
      focusInput(); // Focus lại vào input nếu cần
    }, 200);
  } catch (error) {
    console.error("Error adding tag:", error);
  }
};
import debounce from "lodash/debounce";

const handleSearchTag = debounce(async (keyword) => {
  try {
    const trimmedKeyword = keyword.trim();

    // Chỉ cập nhật tagTitle khi người dùng hoàn tất nhập liệu, không cập nhật khi họ chỉ mới bắt đầu nhập
    tagTitle.value = keyword;
    if (window.navigator.userAgent.includes("Samsung")) {
      nextTick(() => {
        tagTitle.value = keyword;
      });
    }

    const response = await searchTag("", "", "", trimmedKeyword);
    dataSearchTag.value = Array.isArray(response) ? response : [];

    if (
      !dataSearchTag.value.some(
        (tag) => tag.title.toLowerCase() === trimmedKeyword.toLowerCase()
      )
    ) {
      dataSearchTag.value.unshift({ id: "addTAG", title: trimmedKeyword });
    }
  } catch (error) {
    console.error("Error searching tags:", error);
  }
}, 1500);

const createNewTag = async () => {
  const auth = useCookie("auth").value;
  await createTag(
    tagTitle.value.trim(),
    auth?.user?.id,
    useRoute().query.orgId
  );
  await saveTag({ title: tagTitle.value.trim() });
};

const handleRemoveTag = async (tag) => {
  const auth = useCookie("auth").value;
  await removeTag(props.tag._id, tag.id, auth?.user?.id);
  await fetchTags();
};

const handleAddOrCreateTag = () => {
  if (dataSearchTag.value.length === 0 && tagTitle.value) {
    createNewTag();
  } else if (dataSearchTag.value.length === 1 && tagTitle.value) {
    handleAddTag(dataSearchTag.value[0]);
  }
};

// Debounce search for tags
const debounceSearch = (fn, delay = 300) => {
  let timer;
  return (...args) => {
    clearTimeout(timer);
    timer = setTimeout(() => fn(...args), delay);
  };
};

const handleInputChange = debounceSearch(async (event) => {
  await handleSearchTag(event.target.value);
}, 100);

onMounted(fetchTags);
</script>
