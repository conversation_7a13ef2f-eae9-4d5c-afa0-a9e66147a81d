<template>
  <div class="h-full flex flex-col p-4 space-y-4">
    <!-- Header -->
    <div class="flex items-center justify-between">
      <h3 class="text-lg font-semibold text-gray-900">Chi tiết công việc</h3>
    </div>
    <TiptapEditor
      @update:modelValue="handleUpdateContent"
      :modelValue="'Chấm công'"
      :placeholder="'nhập nội dung công việc'"
      :showCharacterCount="true"
      :editable="true"
    ></TiptapEditor>
  </div>
</template>

<script setup lang="ts">
const { handleSaveTextEditor, getContent } = usePortal();
const handleUpdateContent = async (content: string) => {
  const data = {
    type: "CONTENT_ARTICLE_CMS",
    content: content,
    relativeId: "20.13850.7516",
    version: new Date(),
    createdBy: "hung",
  };
  // await handleSaveTextEditor(data);
};
const handleGetContent = async () => {
  try {
    const response = await getContent("CONTENT_ARTICLE_CMS", "20.13850.7516");
    console.log("response", response);
  } catch (error) {
    throw error;
  }
};
const router = useRoute()
</script>
