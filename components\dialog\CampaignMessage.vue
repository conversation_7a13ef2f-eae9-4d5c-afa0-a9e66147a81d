<template>
  <!-- Backdrop with improved animation -->
  <Teleport to="body">
    <Transition
      enter-active-class="transition-all duration-300 ease-out"
      enter-from-class="opacity-0"
      enter-to-class="opacity-100"
      leave-active-class="transition-all duration-200 ease-in"
      leave-from-class="opacity-100"
      leave-to-class="opacity-0"
    >
      <div
        v-if="isVisible"
        class="fixed inset-0 z-50 flex items-center justify-center bg-black/60 backdrop-blur-sm p-4"
        @click.self="cancel"
      >
        <!-- Modal with enhanced styling -->
        <Transition
          enter-active-class="transition-all duration-300 ease-out"
          enter-from-class="opacity-0 scale-95 translate-y-4"
          enter-to-class="opacity-100 scale-100 translate-y-0"
          leave-active-class="transition-all duration-200 ease-in"
          leave-from-class="opacity-100 scale-100 translate-y-0"
          leave-to-class="opacity-0 scale-95 translate-y-4"
        >
          <div
            v-if="isVisible"
            class="bg-white rounded-2xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-hidden"
            @click.stop
          >
            <!-- Header with primary background -->
            <div class="bg-primary px-6 py-4">
              <div class="flex items-center justify-between">
                <!-- Campaign icon -->
                <div class="flex items-center gap-3">
                  <div
                    class="flex items-center justify-center w-8 h-8 bg-white/20 rounded-full backdrop-blur-sm"
                  >
                    <svg
                      class="w-4 h-4 text-white"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path
                        fill-rule="evenodd"
                        d="M12.395 2.553a1 1 0 00-1.45-.385c-.345.23-.614.558-.822.88-.214.33-.403.713-.57 1.116-.334.804-.614 1.768-.84 2.734a31.365 31.365 0 00-.613 3.58 2.64 2.64 0 01-.945-1.067c-.328-.68-.398-1.534-.398-2.654A1 1 0 005.05 6.05 6.981 6.981 0 003 11a7 7 0 1011.95-4.95c-.592-.591-.98-.985-1.348-1.467-.363-.476-.724-1.063-1.207-2.03zM12.12 15.12A3 3 0 017 13s.879.5 2.5.5c0-1 .5-4 1.25-4.5.5 1 .786 1.293 1.371 1.879A2.99 2.99 0 0113 13a2.99 2.99 0 01-.879 2.121z"
                        clip-rule="evenodd"
                      ></path>
                    </svg>
                  </div>
                  <h2 class="text-lg font-semibold text-white">
                    {{ campaignInfo.campaignActionName }}
                  </h2>
                  <span class="text-white">{{
                    `(${formatTimestampV2(
                      campaignInfo.fromDate
                    )} - ${formatTimestampV2(campaignInfo.toDate)})`
                  }}</span>
                </div>

                <!-- Close button -->
                <button
                  @click="cancel"
                  class="flex items-center justify-center w-8 h-8 text-white/80 hover:text-white hover:bg-white/20 rounded-full transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-white/50"
                >
                  <svg
                    class="w-5 h-5"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M6 18L18 6M6 6l12 12"
                    ></path>
                  </svg>
                </button>
              </div>
            </div>

            <!-- Content section -->
            <div class="p-4">
              <!-- sản phẩm tặng -->
              <div
                v-if="campaignInfo?.type === 'PROMOTION_PRODUCT'"
                class="h-[65vh] overflow-y-auto"
              >
                <h2
                  v-if="campaignInfo?.type === 'PROMOTION_PRODUCT'"
                  class="text-lg font-semibold text-gray-900 mb-4"
                >
                  Danh sách sản phẩm tặng
                </h2>

                <!-- Loading Skeleton -->
                <div
                  v-if="isLoadingGifts"
                  class="grid grid-cols-1 lg:grid-cols-2 gap-4"
                >
                  <div
                    v-for="n in 4"
                    :key="`skeleton-${n}`"
                    class="animate-pulse"
                  >
                    <!-- Header Section Skeleton -->
                    <div class="flex items-center gap-2 mb-3">
                      <div class="w-4 h-4 bg-gray-200 rounded"></div>
                      <div class="h-4 bg-gray-200 rounded w-48"></div>
                    </div>

                    <!-- Product Card Skeleton -->
                    <div
                      class="flex gap-3 p-3 bg-white rounded-lg border border-gray-100 shadow-sm"
                    >
                      <!-- Product Image Skeleton -->
                      <div class="flex-shrink-0 relative">
                        <div class="w-14 h-14 bg-gray-200 rounded-lg"></div>
                        <div
                          class="absolute -top-1 -right-1 w-4 h-4 bg-gray-200 rounded-full"
                        ></div>
                      </div>
                      <!-- Product Info Skeleton -->
                      <div class="flex-1 min-w-0 space-y-2">
                        <!-- Title and ID/SKU -->
                        <div class="space-y-1">
                          <div class="h-4 bg-gray-200 rounded w-3/4"></div>
                          <div class="flex items-center gap-2">
                            <div class="w-3 h-3 bg-gray-200 rounded"></div>
                            <div class="h-3 bg-gray-200 rounded w-16"></div>
                            <div class="w-3 h-3 bg-gray-200 rounded"></div>
                            <div class="h-3 bg-gray-200 rounded w-20"></div>
                          </div>
                        </div>

                        <!-- Price Section -->
                        <div class="space-y-1">
                          <div class="flex items-center gap-2">
                            <div class="h-5 bg-gray-200 rounded w-24"></div>
                            <div class="h-4 bg-gray-200 rounded w-20"></div>
                          </div>
                        </div>
                      </div>

                      <!-- Button Skeleton -->
                      <div class="flex-shrink-0">
                        <div class="h-7 bg-gray-200 rounded-md w-16"></div>
                      </div>
                    </div>

                    <!-- Gift Promotion Section Skeleton -->
                    <div class="space-y-3 mt-3">
                      <!-- Gift Header Skeleton -->
                      <div
                        class="bg-blue-50 border border-blue-200 rounded-md p-2"
                      >
                        <div class="flex items-center gap-2">
                          <div class="w-4 h-4 bg-blue-200 rounded"></div>
                          <div class="h-3 bg-blue-200 rounded w-64"></div>
                        </div>
                      </div>

                      <!-- Gift Products Skeleton -->
                      <div class="space-y-2">
                        <div
                          v-for="i in 2"
                          :key="`gift-skeleton-${i}`"
                          class="flex items-center gap-3 p-2 rounded-md border bg-white border-gray-200"
                        >
                          <!-- Gift Image Skeleton -->
                          <div class="flex-shrink-0 relative">
                            <div class="w-10 h-10 bg-gray-200 rounded-lg"></div>
                            <div
                              class="absolute -top-1 -right-1 w-3 h-3 bg-gray-200 rounded-full"
                            ></div>
                          </div>

                          <!-- Gift Info Skeleton -->
                          <div class="flex-1 min-w-0 space-y-2">
                            <div class="flex items-center gap-2">
                              <div class="w-3 h-3 bg-gray-200 rounded"></div>
                              <div class="h-4 bg-gray-200 rounded w-3/4"></div>
                            </div>
                            <div class="flex items-center gap-4">
                              <div class="h-3 bg-gray-200 rounded w-16"></div>
                              <div class="h-3 bg-gray-200 rounded w-20"></div>
                              <div class="h-3 bg-gray-200 rounded w-24"></div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Actual Content -->
                <div
                  v-else-if="
                    !isLoadingGifts && dataProductGiftPromotion?.length
                  "
                  class="grid grid-cols-1 lg:grid-cols-2 gap-4"
                >
                  <div
                    v-for="product in dataProductGiftPromotion"
                    :key="product.id"
                    class="h-fit"
                  >
                    <ItemProductCondition
                      :product="product"
                      :campaign="campaignInfo"
                      @cancel="cancel"
                    ></ItemProductCondition>
                  </div>
                </div>

                <!-- Empty State -->
                <div
                  v-else-if="
                    !isLoadingGifts &&
                    !dataProductGiftPromotion?.length &&
                    campaignInfo?.type === 'PROMOTION_PRODUCT'
                  "
                  class="text-center py-8 text-gray-500"
                >
                  <svg
                    class="w-12 h-12 mx-auto mb-4 text-gray-300"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"
                    />
                  </svg>
                  <p class="text-sm">Không có sản phẩm tặng kèm</p>
                </div>
              </div>
              <div
                v-else
                class="h-[65vh] overflow-y-auto md:col-span-2 md:grid md:grid-cols-2"
              >
                <div v-for="voucher in dataVoucher">
                  <ItemVoucher
                    :voucher="voucher"
                    :order="orderDetail"
                    :customer="customer"
                    :campaignAction="campaignInfo"
                  />
                </div>
              </div>
            </div>
          </div>
        </Transition>
      </div>
    </Transition>
  </Teleport>
</template>

<script setup lang="ts">
interface CampaignInfo {
  id: string;
  campaignActionName: string;
  fromDate: string | number;
  toDate: string | number;
  campaignId: string;
  description?: string;
  type?: string;
  campaignActionId: string;
}

interface Props {
  campaignInfo: CampaignInfo;
}

const emit = defineEmits<{
  confirm: [];
  cancel: [];
  viewDetails: [campaignInfo: CampaignInfo];
}>();

const props = defineProps<Props>();

const isVisible = ref(true);
const orderStore = useOrderStore();
const orderDetail = computed(() => orderStore.orderDetail);
const customer = computed(() => orderStore.customerInOrder);
// Animation and lifecycle management
const cancel = () => {
  isVisible.value = false;
  // Delay emit to allow animation to complete
  setTimeout(() => {
    emit("cancel");
  }, 200);
};
const { searchProductGiftPromotionResponse, suggestVoucher } = useCampaign();
const { getProductById } = useProduct();
const dataProductGiftPromotion = ref<any>([]);
const isLoadingGifts = ref<boolean>(false);

const handleGetProductGiftPromotion = async (
  productIds: string[],
  campaignActionId: string
) => {
  try {
    isLoadingGifts.value = true;
    const response = await searchProductGiftPromotionResponse(
      productIds,
      campaignActionId
    );
    dataProductGiftPromotion.value = response?.content;
    // if (dataProductGiftPromotion.value?.length > 0) {
    //   dataProductGiftPromotion.value.forEach(async (item: any) => {
    //     const response = await getProductById(item?.productId);
    //     console.log("response", response);
    //   });
    // }
  } catch (error) {
    console.error("Error loading gift promotions:", error);
    throw error;
  } finally {
    isLoadingGifts.value = false;
  }
};
const dataVoucher = ref<any>([]);
const handleGetVoucher = async () => {
  try {
    const response = await suggestVoucher(
      customer?.value?.id || "",
      props.campaignInfo?.campaignId,
      props.campaignInfo?.campaignActionId,
      props.campaignInfo?.type === "PROMOTION_BIRTH_DAY"
    );
    dataVoucher.value = response?.content;
  } catch (error) {
    throw error;
  }
};
onMounted(async () => {
  if (props.campaignInfo?.type === "PROMOTION_PRODUCT") {
    await handleGetProductGiftPromotion(
      [],
      props.campaignInfo?.campaignActionId
    );
  } else {
    await handleGetVoucher();
  }
});

// Keyboard event handling
const handleKeydown = (event: KeyboardEvent) => {
  if (event.key === "Escape") {
    cancel();
  }
};

onMounted(() => {
  document.addEventListener("keydown", handleKeydown);
});

onUnmounted(() => {
  document.removeEventListener("keydown", handleKeydown);
});
</script>
