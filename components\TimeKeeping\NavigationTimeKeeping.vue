<template>
  <div class="bg-white rounded-lg border border-gray-200 md:h-screen-50">
    <div class="p-2 space-y-2">
      <!-- Custom Date Range -->
      <div>
        <div class="flex items-center gap-2">
          <div class="w-1/2">
            <label class="block text-sm text-gray-700 mb-1 font-medium"
              >Từ ngày</label
            >
            <input
              type="date"
              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary text-sm"
            />
          </div>
          <div class="w-1/2">
            <label class="block text-sm text-gray-700 mb-1 font-medium"
              >Đ<PERSON><PERSON> ngày</label
            >
            <input
              type="date"
              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary text-sm"
            />
          </div>
        </div>
      </div>

      <!-- Employee Select -->
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-3">
          Chọn nhân viên
        </label>
        <div class="relative">
          <button
            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary text-sm text-left bg-white flex items-center justify-between"
          >
            <span class="text-gray-500"> Chọn nhân viên... </span>

            <svg
              :class="['w-4 h-4 text-gray-400 transition-transform']"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M19 9l-7 7-7-7"
              />
            </svg>
          </button>

          <!-- Dropdown -->
          <!-- <div
            v-if="isEmployeeDropdownOpen"
            class="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg max-h-64 overflow-hidden"
          >
            <div class="p-3 border-b border-gray-200">
              <div class="relative">
                <input
                  v-model="searchEmployee"
                  type="text"
                  placeholder="Tìm kiếm nhân viên..."
                  class="w-full pl-8 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary text-sm"
                  @input="handleEmployeeSearch"
                />
                <svg
                  class="absolute left-2.5 top-2.5 w-4 h-4 text-gray-400"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                  />
                </svg>
              </div>
            </div>

            <div class="max-h-48 overflow-y-auto">
              <div v-if="isLoadingEmployees" class="p-4 text-center">
                <div
                  class="animate-spin w-5 h-5 border-2 border-primary border-t-transparent rounded-full mx-auto"
                ></div>
                <p class="text-sm text-gray-500 mt-2">Đang tải...</p>
              </div>

              <div
                v-else-if="filteredEmployees.length === 0"
                class="p-4 text-center text-gray-500 text-sm"
              >
                {{
                  searchEmployee
                    ? "Không tìm thấy nhân viên"
                    : "Chưa có nhân viên"
                }}
              </div>

              <div v-else>
                <label
                  class="flex items-center p-3 hover:bg-gray-50 cursor-pointer border-b border-gray-100"
                >
                  <input
                    :checked="isAllEmployeesSelected"
                    @change="toggleAllEmployees"
                    type="checkbox"
                    class="w-4 h-4 text-primary border-gray-300 rounded focus:ring-primary/20"
                  />
                  <span class="ml-3 text-sm font-medium text-gray-900">
                    Chọn tất cả ({{ filteredEmployees.length }})
                  </span>
                </label>

                <label
                  v-for="employee in filteredEmployees"
                  :key="employee.id"
                  class="flex items-center p-3 hover:bg-gray-50 cursor-pointer transition-colors"
                >
                  <input
                    v-model="filters.selectedEmployees"
                    :value="employee.id"
                    type="checkbox"
                    class="w-4 h-4 text-primary border-gray-300 rounded focus:ring-primary/20"
                  />
                  <div class="ml-3 flex items-center gap-3 flex-1">
                    <img
                      :src="employee.avatar || 'https://placehold.co/32'"
                      :alt="employee.name"
                      class="w-8 h-8 rounded-full object-cover border border-gray-200"
                    />
                    <div class="flex-1 min-w-0">
                      <p class="text-sm font-medium text-gray-900 truncate">
                        {{ employee.name }}
                      </p>
                      <p class="text-xs text-gray-500 truncate">
                        {{ employee.code }} • {{ employee.department }}
                      </p>
                    </div>
                    <div class="flex items-center gap-1">
                      <div
                        :class="getEmployeeStatusClass(employee.status)"
                        class="w-2 h-2 rounded-full"
                      ></div>
                      <span class="text-xs text-gray-500">
                        {{ getEmployeeStatusText(employee.status) }}
                      </span>
                    </div>
                  </div>
                </label>
              </div>
            </div>

            <div class="p-3 border-t border-gray-200 bg-gray-50">
              <div class="flex justify-between items-center">
                <span class="text-xs text-gray-500">
                  {{ filters.selectedEmployees.length }} nhân viên đã chọn
                </span>
                <div class="flex gap-2">
                  <button
                    @click="clearSelectedEmployees"
                    class="text-xs text-gray-600 hover:text-gray-800"
                  >
                    Xóa tất cả
                  </button>
                  <button
                    @click="closeEmployeeDropdown"
                    class="text-xs bg-primary text-white px-3 py-1 rounded hover:bg-primary/90"
                  >
                    Xong
                  </button>
                </div>
              </div>
            </div>
          </div> -->
        </div>
      </div>
      <!-- task -->
      <div class="border"></div>
      <div class="flex items-center justify-between">
        <label class="font-semibold text-sm">Danh sách công việc</label>
        <div
          @click="handleCreateWorkEffort"
          class="bg-primary text-white px-2 py-1 rounded flex items-center cursor-pointer"
        >
          Bắt đầu
        </div>
      </div>

      <!-- Task List -->
      <div class="space-y-3 mt-3">
        <div
          v-for="task in dataWorkEffort"
          :key="task.id"
          class="bg-white border border-gray-200 rounded-lg p-3 hover:shadow-sm transition-shadow"
        >
          <!-- Task Header -->
          <div class="flex items-start justify-between mb-2">
            <div class="flex-1 min-w-0">
              <h4 class="text-sm font-medium text-gray-900 truncate">
                {{ task?.name }}
              </h4>
              <p class="text-xs text-gray-500 mt-1"></p>
            </div>
          </div>

          <!-- Task Details -->
          <div class="space-y-2">
            <!-- Assignee -->
            <div class="flex items-center gap-2">
              <svg
                class="w-3 h-3 text-gray-400"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
                />
              </svg>
              <span class="text-xs text-gray-600">
                {{ task?.createdBy }}
              </span>
            </div>

            <!-- Time -->
            <div class="flex items-center gap-2">
              <svg
                class="w-3 h-3 text-gray-400"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                />
              </svg>
              <span class="text-xs">{{ task?.createdStamp }}</span>
            </div>

            <!-- Status -->
            <div class="flex items-center justify-between">
              <span
                class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium"
              >
                {{ task?.workEffortTypeId }}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
const emit = defineEmits([
  "filtersChanged",
  "employeeSelected",
  "dateRangeChanged",
  "createWorkEffort",
]);

////
const props = defineProps(["dataWorkEffort", "selectedWorkEffortId"]);
const handleCreateWorkEffort = () => {
  emit("createWorkEffort");
};
</script>
